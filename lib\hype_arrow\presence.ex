defmodule HypeArrow.Presence do
  @moduledoc """
  Provides presence tracking functionality for HypeArrow.

  This module tracks which users are currently viewing specific posts,
  supporting both authenticated users and anonymous users with persistent identifiers.
  """

  use Phoenix.Presence,
    otp_app: :hype_arrow,
    pubsub_server: HypeArrow.PubSub

  alias HypeArrow.Accounts

  @doc """
  Track a user's presence on a specific post.
  """
  def track_post_presence(post_id, user_id, anonymous_id, socket) do
    topic = "post:#{post_id}"
    key = presence_key(user_id, anonymous_id)

    payload = %{
      user_id: user_id,
      anonymous_id: anonymous_id,
      username: get_username(user_id),
      joined_at: System.system_time(:second)
    }

    track(socket, topic, key, payload)
  end

  @doc """
  Get the list of users currently viewing a specific post.
  """
  def list_post_viewers(post_id) do
    topic = "post:#{post_id}"

    list(topic)
    |> Enum.map(fn {_key, %{metas: metas}} ->
      # Get the most recent meta (in case of multiple connections)
      List.first(metas)
    end)
    |> Enum.sort_by(& &1.joined_at)
  end

  @doc """
  Subscribe to presence updates for a specific post.
  """
  def subscribe_to_post_presence(post_id) do
    topic = "post:#{post_id}"
    Phoenix.PubSub.subscribe(HypeArrow.PubSub, topic)
  end

  @doc """
  Generate a unique anonymous identifier.
  """
  def generate_anonymous_id do
    "anon_" <> (:crypto.strong_rand_bytes(8) |> Base.encode16(case: :lower))
  end

  # Private functions

  defp presence_key(user_id, anonymous_id) do
    cond do
      user_id -> "user:#{user_id}"
      anonymous_id -> "anonymous:#{anonymous_id}"
      true -> raise "Either user_id or anonymous_id must be provided"
    end
  end

  defp get_username(nil), do: nil

  defp get_username(user_id) do
    case Accounts.get_user!(user_id) do
      %{email: email} ->
        # Extract username from email (part before @)
        email |> String.split("@") |> List.first()

      _ ->
        "User#{user_id}"
    end
  rescue
    _ -> "User#{user_id}"
  end
end
