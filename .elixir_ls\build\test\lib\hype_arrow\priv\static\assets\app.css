/*
! tailwindcss v3.4.3 | MIT License | https://tailwindcss.com
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #2563eb;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[multiple] {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.hero-arrow-left {
  --hero-arrow-left: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"/></svg>');
  -webkit-mask: var(--hero-arrow-left);
  mask: var(--hero-arrow-left);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-arrow-left-solid {
  --hero-arrow-left-solid: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" data-slot="icon">  <path fill-rule="evenodd" d="M11.03 3.97a.75.75 0 0 1 0 1.06l-6.22 6.22H21a.75.75 0 0 1 0 1.5H4.81l6.22 6.22a.75.75 0 1 1-1.06 1.06l-7.5-7.5a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 0 1 1.06 0Z" clip-rule="evenodd"/></svg>');
  -webkit-mask: var(--hero-arrow-left-solid);
  mask: var(--hero-arrow-left-solid);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-arrow-path {
  --hero-arrow-path: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"/></svg>');
  -webkit-mask: var(--hero-arrow-path);
  mask: var(--hero-arrow-path);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-arrow-right {
  --hero-arrow-right: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"/></svg>');
  -webkit-mask: var(--hero-arrow-right);
  mask: var(--hero-arrow-right);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-bars-2 {
  --hero-bars-2: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 9h16.5m-16.5 6.75h16.5"/></svg>');
  -webkit-mask: var(--hero-bars-2);
  mask: var(--hero-bars-2);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-bars-3 {
  --hero-bars-3: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"/></svg>');
  -webkit-mask: var(--hero-bars-3);
  mask: var(--hero-bars-3);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-bars-4 {
  --hero-bars-4: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 5.25h16.5m-16.5 4.5h16.5m-16.5 4.5h16.5m-16.5 4.5h16.5"/></svg>');
  -webkit-mask: var(--hero-bars-4);
  mask: var(--hero-bars-4);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-bell {
  --hero-bell: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"/></svg>');
  -webkit-mask: var(--hero-bell);
  mask: var(--hero-bell);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-bookmark {
  --hero-bookmark: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"/></svg>');
  -webkit-mask: var(--hero-bookmark);
  mask: var(--hero-bookmark);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-chat-bubble-left {
  --hero-chat-bubble-left: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 0 1 1.037-.443 48.282 48.282 0 0 0 5.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"/></svg>');
  -webkit-mask: var(--hero-chat-bubble-left);
  mask: var(--hero-chat-bubble-left);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-check {
  --hero-check: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5"/></svg>');
  -webkit-mask: var(--hero-check);
  mask: var(--hero-check);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-check-circle {
  --hero-check-circle: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/></svg>');
  -webkit-mask: var(--hero-check-circle);
  mask: var(--hero-check-circle);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-cog-6-tooth {
  --hero-cog-6-tooth: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"/>  <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/></svg>');
  -webkit-mask: var(--hero-cog-6-tooth);
  mask: var(--hero-cog-6-tooth);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-envelope {
  --hero-envelope: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"/></svg>');
  -webkit-mask: var(--hero-envelope);
  mask: var(--hero-envelope);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-exclamation-circle-mini {
  --hero-exclamation-circle-mini: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">  <path fill-rule="evenodd" d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-8-5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-4.5A.75.75 0 0 1 10 5Zm0 10a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" clip-rule="evenodd"/></svg>');
  -webkit-mask: var(--hero-exclamation-circle-mini);
  mask: var(--hero-exclamation-circle-mini);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
}

.hero-exclamation-triangle {
  --hero-exclamation-triangle: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"/></svg>');
  -webkit-mask: var(--hero-exclamation-triangle);
  mask: var(--hero-exclamation-triangle);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-heart {
  --hero-heart: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"/></svg>');
  -webkit-mask: var(--hero-heart);
  mask: var(--hero-heart);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-home {
  --hero-home: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/></svg>');
  -webkit-mask: var(--hero-home);
  mask: var(--hero-home);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-information-circle {
  --hero-information-circle: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"/></svg>');
  -webkit-mask: var(--hero-information-circle);
  mask: var(--hero-information-circle);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-information-circle-mini {
  --hero-information-circle-mini: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">  <path fill-rule="evenodd" d="M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-7-4a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM9 9a.75.75 0 0 0 0 1.5h.253a.25.25 0 0 1 .244.304l-.459 2.066A1.75 1.75 0 0 0 10.747 15H11a.75.75 0 0 0 0-1.5h-.253a.25.25 0 0 1-.244-.304l.459-2.066A1.75 1.75 0 0 0 9.253 9H9Z" clip-rule="evenodd"/></svg>');
  -webkit-mask: var(--hero-information-circle-mini);
  mask: var(--hero-information-circle-mini);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.25rem;
  height: 1.25rem;
}

.hero-magnifying-glass {
  --hero-magnifying-glass: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"/></svg>');
  -webkit-mask: var(--hero-magnifying-glass);
  mask: var(--hero-magnifying-glass);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-minus {
  --hero-minus: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14"/></svg>');
  -webkit-mask: var(--hero-minus);
  mask: var(--hero-minus);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-pencil {
  --hero-pencil: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"/></svg>');
  -webkit-mask: var(--hero-pencil);
  mask: var(--hero-pencil);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-plus {
  --hero-plus: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"/></svg>');
  -webkit-mask: var(--hero-plus);
  mask: var(--hero-plus);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-share {
  --hero-share: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z"/></svg>');
  -webkit-mask: var(--hero-share);
  mask: var(--hero-share);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-trash {
  --hero-trash: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"/></svg>');
  -webkit-mask: var(--hero-trash);
  mask: var(--hero-trash);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-user {
  --hero-user: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"/></svg>');
  -webkit-mask: var(--hero-user);
  mask: var(--hero-user);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-user-circle {
  --hero-user-circle: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/></svg>');
  -webkit-mask: var(--hero-user-circle);
  mask: var(--hero-user-circle);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-x-circle {
  --hero-x-circle: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/></svg>');
  -webkit-mask: var(--hero-x-circle);
  mask: var(--hero-x-circle);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-x-mark {
  --hero-x-mark: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"/></svg>');
  -webkit-mask: var(--hero-x-mark);
  mask: var(--hero-x-mark);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-x-mark-solid {
  --hero-x-mark-solid: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" data-slot="icon">  <path fill-rule="evenodd" d="M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd"/></svg>');
  -webkit-mask: var(--hero-x-mark-solid);
  mask: var(--hero-x-mark-solid);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

.hero-eye {
  --hero-eye: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">  <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"/>  <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/></svg>');
  -webkit-mask: var(--hero-eye);
  mask: var(--hero-eye);
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  background-color: currentColor;
  vertical-align: middle;
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
}

/* Layout Components */

.hype-container {
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-bottom: 3rem;
}

@media (min-width: 640px) {
  .hype-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .hype-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.hype-navbar {
  position: sticky;
  top: 0px;
  z-index: 50;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(26 35 51 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(10 14 23 / var(--tw-bg-opacity));
}

.hype-sidebar {
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 30;
  height: 100%;
  width: 16rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  overflow-y: auto;
  border-right-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(26 35 51 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(15 22 35 / var(--tw-bg-opacity));
  padding-top: 4rem;
  transition-property: transform;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.hype-main-content {
  min-height: 100vh;
  overflow-y: auto;
  --tw-bg-opacity: 1;
  background-color: rgb(10 14 23 / var(--tw-bg-opacity));
  padding-top: 1.5rem;
  padding-bottom: 3rem;
  transition-property: all;
  transition-duration: 300ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  scroll-behavior: smooth;
  background-image: linear-gradient(135deg, #0A0E17 0%, #0F1623 50%, #1A2333 100%);
}

/* Mobile sidebar styles */

@media (max-width: 768px) {
  .hype-sidebar {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .hype-sidebar.open {
    display: block !important;
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  /* Ensure the sidebar is above other content */

  #sidebar-overlay {
    position: fixed;
    inset: 0px;
    z-index: 20;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity));
    --tw-bg-opacity: 0.5;
  }
}

/* Navigation Components */

.hype-nav-link {
  display: flex;
  align-items: center;
  border-radius: 0.375rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.expanded-post .hype-nav-link.flex-wrap.mt-3 {
  margin-top: 1.5rem;
}

.hype-nav-link:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(26 35 51 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hype-nav-link-active {
  --tw-bg-opacity: 1;
  background-color: rgb(26 35 51 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

/* Button Components */

.hype-btn {
  display: inline-flex;
  align-items: center;
  border-radius: 0.375rem;
  border-width: 1px;
  border-color: transparent;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.hype-btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 144 255 / var(--tw-ring-opacity));
  --tw-ring-offset-width: 2px;
}

.hype-btn-primary {
  background-image: linear-gradient(135deg, #36C7FF 0%, #0E90FF 50%, #8A2BE2 100%);
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hype-btn-primary:hover {
  opacity: 0.9;
}

.hype-btn-secondary {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(14 144 255 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(26 35 51 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hype-btn-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(15 22 35 / var(--tw-bg-opacity));
}

/* Card Components */

/* Card styles moved to transparent_cards.css */

/* Post Components */

/* Post styles moved to components */

/* Form Components */

/* Utility Components */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.visible {
  visibility: visible;
}

.collapse {
  visibility: collapse;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.-inset-y-px {
  top: -1px;
  bottom: -1px;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.-left-4 {
  left: -1rem;
}

.-right-4 {
  right: -1rem;
}

.-top-2 {
  top: -0.5rem;
}

.bottom-0 {
  bottom: 0px;
}

.left-0 {
  left: 0px;
}

.left-3 {
  left: 0.75rem;
}

.left-\[40rem\] {
  left: 40rem;
}

.right-0 {
  right: 0px;
}

.right-10 {
  right: 2.5rem;
}

.right-4 {
  right: 1rem;
}

.right-5 {
  right: 1.25rem;
}

.top-0 {
  top: 0px;
}

.top-2 {
  top: 0.5rem;
}

.top-4 {
  top: 1rem;
}

.top-6 {
  top: 1.5rem;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

.z-\[100\] {
  z-index: 100;
}

.-m-3 {
  margin: -0.75rem;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-my-0 {
  margin-top: -0px;
  margin-bottom: -0px;
}

.-my-0\.5 {
  margin-top: -0.125rem;
  margin-bottom: -0.125rem;
}

.-my-4 {
  margin-top: -1rem;
  margin-bottom: -1rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-1\.5 {
  margin-right: 0.375rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mt-0 {
  margin-top: 0px;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-11 {
  margin-top: 2.75rem;
}

.mt-14 {
  margin-top: 3.5rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.block {
  display: block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.h-1 {
  height: 0.25rem;
}

.h-1\.5 {
  height: 0.375rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-16 {
  height: 4rem;
}

.h-2 {
  height: 0.5rem;
}

.h-2\.5 {
  height: 0.625rem;
}

.h-3 {
  height: 0.75rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-8 {
  height: 2rem;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.max-h-40 {
  max-height: 10rem;
}

.min-h-\[6rem\] {
  min-height: 6rem;
}

.min-h-full {
  min-height: 100%;
}

.w-0 {
  width: 0px;
}

.w-1 {
  width: 0.25rem;
}

.w-1\.5 {
  width: 0.375rem;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-1\/4 {
  width: 25%;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: 0.5rem;
}

.w-2\.5 {
  width: 0.625rem;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-24 {
  width: 6rem;
}

.w-3 {
  width: 0.75rem;
}

.w-3\/4 {
  width: 75%;
}

.w-4 {
  width: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-64 {
  width: 16rem;
}

.w-72 {
  width: 18rem;
}

.w-8 {
  width: 2rem;
}

.w-80 {
  width: 20rem;
}

.w-\[40rem\] {
  width: 40rem;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-sm {
  max-width: 24rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.resize {
  resize: both;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}

.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.-space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-zinc-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(244 244 245 / var(--tw-divide-opacity));
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-y-auto {
  overflow-y: auto;
}

.scroll-smooth {
  scroll-behavior: smooth;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.text-balance {
  text-wrap: balance;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-t {
  border-top-width: 1px;
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.border-blue-700 {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity));
}

.border-brand {
  --tw-border-opacity: 1;
  border-color: rgb(14 144 255 / var(--tw-border-opacity));
}

.border-brand-light {
  --tw-border-opacity: 1;
  border-color: rgb(54 199 255 / var(--tw-border-opacity));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-gray-400 {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}

.border-gray-600 {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}

.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}

.border-gray-800 {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity));
}

.border-green-700 {
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity));
}

.border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity));
}

.border-rose-400 {
  --tw-border-opacity: 1;
  border-color: rgb(251 113 133 / var(--tw-border-opacity));
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-yellow-700 {
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity));
}

.border-zinc-200 {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity));
}

.border-zinc-300 {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 216 / var(--tw-border-opacity));
}

.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}

.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}

.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}

.bg-background-dark {
  --tw-bg-opacity: 1;
  background-color: rgb(10 14 23 / var(--tw-bg-opacity));
}

.bg-background-light\/50 {
  background-color: rgb(26 35 51 / 0.5);
}

.bg-background\/90 {
  background-color: rgb(15 22 35 / 0.9);
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.bg-blue-900\/80 {
  background-color: rgb(30 58 138 / 0.8);
}

.bg-brand {
  --tw-bg-opacity: 1;
  background-color: rgb(14 144 255 / var(--tw-bg-opacity));
}

.bg-brand\/5 {
  background-color: rgb(14 144 255 / 0.05);
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity));
}

.bg-green-900\/80 {
  background-color: rgb(20 83 45 / 0.8);
}

.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity));
}

.bg-red-900\/80 {
  background-color: rgb(127 29 29 / 0.8);
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-yellow-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity));
}

.bg-yellow-900\/80 {
  background-color: rgb(113 63 18 / 0.8);
}

.bg-zinc-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.bg-zinc-50\/90 {
  background-color: rgb(250 250 250 / 0.9);
}

.bg-zinc-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(24 24 27 / var(--tw-bg-opacity));
}

.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}

.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}

.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-opacity-70 {
  --tw-bg-opacity: 0.7;
}

.bg-gradient-brand {
  background-image: linear-gradient(135deg, #36C7FF 0%, #0E90FF 50%, #8A2BE2 100%);
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}

.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}

.fill-zinc-400 {
  fill: #a1a1aa;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-14 {
  padding: 3.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-\[0\.8125rem\] {
  font-size: 0.8125rem;
}

.text-\[2rem\] {
  font-size: 2rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.leading-10 {
  line-height: 2.5rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-8 {
  line-height: 2rem;
}

.leading-relaxed {
  line-height: 1.625;
}

.tracking-tighter {
  letter-spacing: -0.05em;
}

.text-blue-400 {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.text-brand {
  --tw-text-opacity: 1;
  color: rgb(14 144 255 / var(--tw-text-opacity));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.text-green-400 {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity));
}

.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.text-rose-600 {
  --tw-text-opacity: 1;
  color: rgb(225 29 72 / var(--tw-text-opacity));
}

.text-transparent {
  color: transparent;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}

.text-zinc-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity));
}

.text-zinc-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity));
}

.text-zinc-700 {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity));
}

.text-zinc-800 {
  --tw-text-opacity: 1;
  color: rgb(39 39 42 / var(--tw-text-opacity));
}

.text-zinc-900 {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity));
}

.text-white\/50 {
  color: rgb(255 255 255 / 0.5);
}

.text-white\/60 {
  color: rgb(255 255 255 / 0.6);
}

.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}

.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}

.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}

.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}

.placeholder-white\/50::-moz-placeholder {
  color: rgb(255 255 255 / 0.5);
}

.placeholder-white\/50::placeholder {
  color: rgb(255 255 255 / 0.5);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-30 {
  opacity: 0.3;
}

.opacity-50 {
  opacity: 0.5;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-zinc-700\/10 {
  --tw-shadow-color: rgb(63 63 70 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.outline {
  outline-style: solid;
}

.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-inset {
  --tw-ring-inset: inset;
}

.ring-background-dark {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(10 14 23 / var(--tw-ring-opacity));
}

.ring-red-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}

.ring-zinc-700\/10 {
  --tw-ring-color: rgb(63 63 70 / 0.1);
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
          backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.\[scrollbar-gutter\:stable\] {
  scrollbar-gutter: stable;
}

/* This file is for your main application CSS */

/*
 * Expanded Item Styles
 * These styles are specific to expanded items and not included in transparent_cards.css
 */

/* Add extra spacing when grid has an expanded item */

.content-grid.has-expanded-item,
.posts-grid.has-expanded-post {
  gap: 2.5rem;
  /* Increase gap between items when one is expanded */
  margin-bottom: 2rem;
  /* Add extra bottom margin */
  padding-bottom: 1rem;
  /* Add padding at the bottom */
}

/* Add extra spacing to items that come after an expanded item */

.content-grid.has-expanded-item .post-card:not(.expanded-post),
.posts-grid.has-expanded-post .post-card:not(.expanded-post) {
  position: relative;
  /* Ensure proper positioning */
  z-index: 1;
  /* Lower than expanded item */
  margin-top: 0.75rem;
  /* Add top margin to all non-expanded items */
  margin-bottom: 0.75rem;
  /* Add bottom margin to all non-expanded items */
}

/* Mobile-specific styles for expanded items */

@media (max-width: 767px) {
  /* Ensure expanded items have extra spacing on mobile */

  .expanded-post {
    padding-bottom: 1.5rem !important;
    transform: scale(1.1) !important;
    /* Slightly larger scale on mobile */
  }

  /* Add extra spacing to the grid when an item is expanded */

  .content-grid.has-expanded-item,
  .posts-grid.has-expanded-post {
    gap: 3rem !important;
    margin-bottom: 2.5rem !important;
  }
}

/* Grid layout styles moved to grid_controls.css */

/* Full-width row expansion styles */

.expanded-post {
  /* Enhanced visual appearance */
  border: 1px solid rgba(54, 199, 255, 0.5) !important;
  background-color: rgba(20, 30, 50, 0.95) !important;
  /* Slightly different background */
  /* Smooth transition */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* Ensure content is visible */
  max-height: none !important;
  overflow: visible !important;
  /* Increase height for better content visibility */
  min-height: 350px !important;
  /* Ensure it stands out and overlays other posts */
  position: relative;
  z-index: 100;
  /* Add a more noticeable glow effect */
  box-shadow: 0 0 20px rgba(54, 199, 255, 0.5), 0 10px 25px rgba(0, 0, 0, 0.5) !important;
  /* Full width styles */
  width: 100% !important;
  margin: 1.5rem 0 !important;
  justify-self: stretch !important;
  /* Grid properties for full-width expansion - set immediately by CSS */
  /* This eliminates the need for JavaScript grid positioning */
  grid-column: 1 / -1 !important;
}

/* Expanded post content */

.expanded-post .post-card-body {
  max-height: none !important;
  overflow: visible !important;
  word-wrap: break-word !important;
  /* Ensure long words don't overflow */
  overflow-wrap: break-word !important;
  /* Modern browsers */
  padding: 1rem !important;
  /* Add padding for better content display */
}

.expanded-post p {
  max-height: none !important;
  overflow: visible !important;
  display: block !important;
  -webkit-line-clamp: unset !important;
  word-wrap: break-word !important;
  /* Ensure long words don't overflow */
  overflow-wrap: break-word !important;
  /* Modern browsers */
  -webkit-hyphens: auto !important;
          hyphens: auto !important;
  /* Add hyphens for very long words */
}

/* Fix for very long content */

.expanded-post .post-card-body:after {
  content: "";
  display: block;
  height: 1rem;
  /* Add extra space at the bottom */
  width: 100%;
}

/* Ensure expanded post content is properly displayed */

.expanded-post .post-card-body {
  overflow-y: auto !important;
  max-height: 600px !important;
  /* Increased max height for better content visibility */
  padding: 1.5rem !important;
  /* Add more padding for better readability */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* Smooth transition */
}

/* Ensure expanded post has appropriate dimensions */

.expanded-post {
  height: auto !important;
  min-height: 350px !important;
  /* Ensure minimum height for content */
  /* Animation for expanding - ONLY for client-side expansions, not server-rendered */
  /* Server-rendered posts should not animate to prevent visual flicker */
}

/* Animation for client-side post expansion only */

.expanded-post:not([data-expanded-by-server]):not([data-expanded-by-dom-loaded]) {
  animation: expandPost 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animation for expanding posts */

@keyframes expandPost {
  from {
    opacity: 0.8;
    transform: scale(0.98);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Add a close button to expanded posts */

.expanded-post .post-collapse-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  z-index: 20;
  line-height: 1;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
  transition: opacity 0.2s ease;
  border: none;
  padding: 0;
}

.expanded-post .post-collapse-button:hover {
  opacity: 1;
}

/*
 * Card Styles
 * This is the single source of truth for all card styles
 */

/* Base card styles */

.post-card, .card-container {
  /* Structure */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 0.5rem;
  /* Appearance */
  background-color: rgba(15, 22, 35, 0.7);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  box-shadow: 0 4px 15px -3px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(54, 199, 255, 0.1);
  border: 1px solid rgba(26, 35, 51, 0.8);
  /* Animation */
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effect for non-expanded cards */

.post-card:hover:not(.expanded-post),
.card-container:hover:not(.expanded-card) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px -3px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(54, 199, 255, 0.2);
}

/* Card header */

.post-card-header, .card-header {
  padding: 1rem 1rem;
}

/* Card title spacing */

.post-card-header h3, .card-header h3 {
  padding-bottom: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Metadata divider */

.post-card-header .text-xs, .card-header .text-xs {
  border-bottom: 1px solid rgba(26, 35, 51, 0.2);
  padding-bottom: 0.25rem;
  margin-bottom: 0;
}

/* Card body */

.post-card-body, .card-body {
  padding: 0 1rem 1rem 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Card footer */

.post-card-footer, .card-footer {
  padding: 0.75rem 1rem;
  margin-top: auto;
  background-color: rgba(26, 35, 51, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-top: 1px solid rgba(26, 35, 51, 0.8);
}

/* Make sure the username doesn't overflow on small screens */

.post-card-footer .truncate {
  max-width: 140px;
}

/* Style the Read More button */

.post-card-footer button {
  padding: 0.25rem 0;
  margin-top: 0.25rem;
}

/* Expanded post styles */

.expanded-post {
  position: relative;
  background-color: rgba(15, 22, 35, 0.85);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(54, 199, 255, 0.2);
  border-color: rgba(54, 199, 255, 0.2);
  z-index: 10;
  scroll-margin: calc(50vh - 200px);
  /* Full-width expansion styles */
  box-sizing: border-box !important;
  padding-bottom: 1rem !important;
  /* Note: grid-column and grid-row are set dynamically by JavaScript */
  /* This ensures the post stays in its original row while spanning the full width */
  width: 100% !important;
  justify-self: stretch !important;
}

/* Expanded post header */

.expanded-post .post-card-header {
  padding: 1.25rem 1.25rem 0.75rem 1.25rem;
}

/* Expanded post title */

.expanded-post h3 {
  font-size: 1.5rem;
  line-height: 1.75;
  padding-bottom: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Expanded post body */

.expanded-post .post-card-body {
  padding: 0.5rem 1.25rem 1.25rem 1.25rem;
  max-height: 500px;
  /* Increased max height */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  /* Smooth scrolling on iOS */
}

/* Expanded post content */

.expanded-post p {
  max-height: none;
  overflow: visible;
  font-size: 1rem;
  line-height: 1.5;
  word-wrap: break-word;
  /* Ensure long words don't overflow */
  overflow-wrap: break-word;
  /* Modern browsers */
  -webkit-hyphens: auto;
          hyphens: auto;
  /* Add hyphens for very long words */
}

/* Expanded post footer */

.expanded-post .post-card-footer {
  padding: 0.75rem 1rem;
  background-color: rgba(26, 35, 51, 0.7);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  border-top: 1px solid rgba(54, 199, 255, 0.1);
}

/* Expanded post actions */

.expanded-post .flex.flex-wrap.mt-3 {
  margin-top: 1.5rem;
}

/* Custom scrollbar for expanded post content */

.expanded-post .post-card-body::-webkit-scrollbar {
  width: 6px;
}

.expanded-post .post-card-body::-webkit-scrollbar-track {
  background: rgba(26, 35, 51, 0.3);
  border-radius: 10px;
}

.expanded-post .post-card-body::-webkit-scrollbar-thumb {
  background: rgba(14, 144, 255, 0.5);
  border-radius: 10px;
}

.expanded-post .post-card-body::-webkit-scrollbar-thumb:hover {
  background: rgba(14, 144, 255, 0.7);
}

/* Animation for posts moving to make room */

.content-grid.has-expanded-item .post-card:not(.expanded-post),
.posts-grid.has-expanded-post .post-card:not(.expanded-post) {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              grid-row 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              margin 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* Add extra margin to cards after expanded post */
  margin-top: 1rem;
  /* Ensure posts don't try to fill the same row as the expanded post */
  grid-column: auto !important;
  /* The grid-row property will be set dynamically by JavaScript */
  /* to ensure posts in the same row are pushed below */
}

/* Full-width row expansion styles */

.content-grid.has-expanded-item .expanded-post,
.posts-grid.has-expanded-post .expanded-post {
  /* Full width styles */
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  /* Add some padding for better content display */
  padding: 1.5rem !important;
  /* Ensure proper z-index for overlapping */
  position: relative;
  z-index: 10;
  /* Animation for smooth transition */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* Ensure the post stands out */
  margin: 1.5rem 0 !important;
  box-shadow: 0 0 20px rgba(54, 199, 255, 0.5), 0 10px 25px rgba(0, 0, 0, 0.5) !important;
  /* Note: grid-column and grid-row are set dynamically by JavaScript */
  /* This ensures the post stays in its original row while spanning the full width */
}

/* Responsive styles */

/* Small screens (mobile) */

@media (max-width: 479px) {
  /* Ensure expanded posts have extra spacing on small screens */

  .expanded-post {
    margin: 2.5rem 0 !important;
    /* Increased margin for small screens */
    padding-bottom: 1.5rem !important;
  }

  /* Increase spacing in the grid when a post is expanded */

  .content-grid.has-expanded-item,
  .posts-grid.has-expanded-post {
    gap: 3rem !important;
    /* Larger gap for small screens */
  }

  /* Ensure the post body has a reasonable max height on small screens */

  .expanded-post .post-card-body {
    max-height: 400px;
    /* Slightly smaller on mobile */
  }
}

@media (min-width: 480px) {
  .post-card-footer {
    padding: 0.75rem;
  }

  .post-card-footer .truncate {
    max-width: 180px;
  }

  .post-card-footer .grid {
    grid-template-columns: auto auto;
    align-items: center;
    justify-content: space-between;
  }

  .post-card-footer button {
    width: auto;
    padding: 0.25rem 0.5rem;
    margin-top: 0;
  }

  .post-card-footer .text-center {
    text-align: right;
  }

  /* Medium-sized screens */

  .expanded-post {
    margin: 2.25rem 0 !important;
  }

  .content-grid.has-expanded-item,
  .posts-grid.has-expanded-post {
    gap: 2.75rem !important;
  }
}

@media (min-width: 640px) {
  .post-card-footer {
    padding: 0.75rem 1rem;
  }

  .expanded-post .post-card-footer {
    padding: 1rem 1.25rem;
  }

  .post-card-footer .truncate {
    max-width: 200px;
  }

  .post-card-footer .icon {
    height: 1.25rem;
    width: 1.25rem;
    margin-right: 0.375rem;
  }

  .post-card-footer span,
  .post-card-footer button {
    font-size: 0.875rem;
  }
}

/* Infinite scroll styles */

.infinite-scroll-container {
  position: relative;
}

.infinite-scroll-trigger {
  height: 20px;
  width: 100%;
  margin-top: 1rem;
}

.infinite-scroll-loading {
  width: 100%;
  padding: 1rem 0;
  text-align: center;
}

.infinite-scroll-end {
  width: 100%;
  padding: 1rem 0;
  text-align: center;
  color: rgba(156, 163, 175, 0.8);
  font-size: 0.875rem;
}

/* Loading animation */

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }
}

.infinite-scroll-loading .animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/*
 * Grid Size Controls
 * Styles for the grid size control buttons and grid layouts
 */

/* Grid size control buttons */

.grid-size-controls {
  background-color: rgba(15, 22, 35, 0.7);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 4px 15px -3px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(54, 199, 255, 0.1);
  border: 1px solid rgba(26, 35, 51, 0.8);
}

.grid-size-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.375rem;
  background-color: rgba(26, 35, 51, 0.6);
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.2s ease-in-out;
}

.grid-size-btn:hover {
  background-color: rgba(54, 199, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.grid-size-btn.active {
  background-color: rgba(54, 199, 255, 0.4);
  color: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 2px rgba(54, 199, 255, 0.6);
  transform: translateY(-1px);
}

/* Grid layouts */

/* Default (mobile): 1 column */

.content-grid, .posts-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
  grid-auto-flow: row;
  grid-auto-rows: auto;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* When a post is expanded, increase the gap to provide more space */

.content-grid.has-expanded-item, .posts-grid.has-expanded-post {
  gap: 2.5rem;
  /* Use dense packing to fill gaps efficiently when posts are repositioned */
  grid-auto-flow: row dense;
  /* Ensure grid can accommodate the repositioned items */
  grid-auto-rows: auto;
}

/* Medium screens (tablet) */

@media (min-width: 768px) {
  /* Grid size 1: Force 1 column */

  .content-grid.grid-size-1, .posts-grid.grid-size-1 {
    grid-template-columns: repeat(1, 1fr);
  }

  /* Grid size 2: 2 columns */

  .content-grid.grid-size-2, .posts-grid.grid-size-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Grid size 3: 2 columns on medium screens */

  .content-grid.grid-size-3, .posts-grid.grid-size-3 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Grid size 4: 2 columns on medium screens */

  .content-grid.grid-size-4, .posts-grid.grid-size-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Large screens (desktop) */

@media (min-width: 1024px) {
  /* Grid size 1: Force 1 column */

  .content-grid.grid-size-1, .posts-grid.grid-size-1 {
    grid-template-columns: repeat(1, 1fr);
  }

  /* Grid size 2: 2 columns */

  .content-grid.grid-size-2, .posts-grid.grid-size-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Grid size 3: 3 columns */

  .content-grid.grid-size-3, .posts-grid.grid-size-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  /* Grid size 4: 3 columns on large screens */

  .content-grid.grid-size-4, .posts-grid.grid-size-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Extra large screens */

@media (min-width: 1280px) {
  /* Grid size 4: 4 columns on extra large screens */

  .content-grid.grid-size-4, .posts-grid.grid-size-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Expanded items styling */

.content-grid.has-expanded-item .expanded-card,
.posts-grid.has-expanded-post .expanded-post {
  /* Position and z-index */
  position: relative;
  z-index: 10;
  /* Ensure the expanded post takes up the full width */
  width: 100% !important;
  min-width: 100% !important;
  max-width: 100% !important;
  justify-self: stretch !important;
  /* Add animation for smooth transition */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  /* Note: grid-column and grid-row are set dynamically by JavaScript */
  /* This ensures the post stays in its original row while spanning the full width */
}

/*
 * Modal Styles
 * Custom styles for the modal component to match HypeArrow theme
 */

/* Modal background overlay */

[id$="-bg"] {
  background-color: rgba(10, 14, 23, 0.8) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* Modal container */

.focus-wrap {
  background-color: rgba(15, 22, 35, 0.95) !important;
  border: 1px solid rgba(54, 199, 255, 0.2) !important;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(54, 199, 255, 0.1) !important;
  color: white !important;
  border-radius: 0.75rem !important;
  padding: 1.5rem !important;
}

/* Override default white background */

.focus-wrap div {
  background-color: transparent !important;
}

/* Modal title */

[id$="-content"] h2 {
  color: white !important;
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin-bottom: 1.5rem !important;
  background: linear-gradient(135deg, #36C7FF 0%, #0E90FF 50%, #8A2BE2 100%);
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  text-align: center !important;
}

/* Close button */

.focus-wrap button[aria-label="close"] {
  color: white !important;
  opacity: 0.7 !important;
}

.focus-wrap button[aria-label="close"]:hover {
  opacity: 1 !important;
}

/* Form inputs */

.focus-wrap input[type="text"],
.focus-wrap textarea {
  background-color: rgba(26, 35, 51, 0.8) !important;
  border: 1px solid rgba(54, 199, 255, 0.2) !important;
  color: white !important;
  border-radius: 0.375rem !important;
}

.focus-wrap input[type="text"]:focus,
.focus-wrap textarea:focus {
  border-color: rgba(54, 199, 255, 0.5) !important;
  box-shadow: 0 0 0 2px rgba(54, 199, 255, 0.2) !important;
}

/* Form labels */

.focus-wrap label {
  color: rgba(255, 255, 255, 0.8) !important;
  font-weight: 500 !important;
}

/* Error messages */

.focus-wrap .text-rose-600 {
  color: #ff4d7c !important;
}

/* Form buttons */

.focus-wrap .hype-btn-primary {
  background: linear-gradient(135deg, #36C7FF 0%, #0E90FF 50%, #8A2BE2 100%) !important;
  color: white !important;
  border: none !important;
  transition: opacity 0.2s ease-in-out !important;
}

.focus-wrap .hype-btn-primary:hover {
  opacity: 0.9 !important;
}

.focus-wrap .hype-btn-secondary {
  background-color: rgba(26, 35, 51, 0.8) !important;
  color: white !important;
  border: 1px solid rgba(54, 199, 255, 0.3) !important;
  transition: background-color 0.2s ease-in-out !important;
}

.focus-wrap .hype-btn-secondary:hover {
  background-color: rgba(54, 199, 255, 0.2) !important;
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:bg-background-light:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(26 35 51 / var(--tw-bg-opacity));
}

.hover\:bg-brand-light:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(54 199 255 / var(--tw-bg-opacity));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.hover\:bg-zinc-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.hover\:bg-zinc-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}

.hover\:text-brand:hover {
  --tw-text-opacity: 1;
  color: rgb(14 144 255 / var(--tw-text-opacity));
}

.hover\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:text-zinc-700:hover {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity));
}

.hover\:text-zinc-900:hover {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:opacity-40:hover {
  opacity: 0.4;
}

.focus\:border-rose-400:focus {
  --tw-border-opacity: 1;
  border-color: rgb(251 113 133 / var(--tw-border-opacity));
}

.focus\:border-zinc-400:focus {
  --tw-border-opacity: 1;
  border-color: rgb(161 161 170 / var(--tw-border-opacity));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-inset:focus {
  --tw-ring-inset: inset;
}

.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

.focus\:ring-brand:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 144 255 / var(--tw-ring-opacity));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.active\:text-white\/80:active {
  color: rgb(255 255 255 / 0.8);
}

.group:hover .group-hover\:bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-zinc-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:fill-zinc-600 {
  fill: #52525b;
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.phx-submit-loading.phx-submit-loading\:opacity-75 {
  opacity: 0.75;
}

.phx-submit-loading .phx-submit-loading\:opacity-75 {
  opacity: 0.75;
}

@media (min-width: 640px) {
  .sm\:inline-flex {
    display: inline-flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:w-96 {
    width: 24rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:translate-y-0 {
    --tw-translate-y: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-100 {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:scale-95 {
    --tw-scale-x: .95;
    --tw-scale-y: .95;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:overflow-visible {
    overflow: visible;
  }

  .sm\:rounded-l-xl {
    border-top-left-radius: 0.75rem;
    border-bottom-left-radius: 0.75rem;
  }

  .sm\:rounded-r-xl {
    border-top-right-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:leading-6 {
    line-height: 1.5rem;
  }

  .group:hover .sm\:group-hover\:scale-105 {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
}

@media (min-width: 768px) {
  .md\:ml-64 {
    margin-left: 16rem;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-12 {
    height: 3rem;
  }

  .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:pr-4 {
    padding-right: 1rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

@media (min-width: 1280px) {
  .xl\:left-\[50rem\] {
    left: 50rem;
  }

  .xl\:px-28 {
    padding-left: 7rem;
    padding-right: 7rem;
  }

  .xl\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}
