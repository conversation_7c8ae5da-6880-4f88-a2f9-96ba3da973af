defmodule HypeArrowWeb.PresenceComponents do
  @moduledoc """
  Provides UI components for displaying user presence information.
  """

  use Phoenix.Component

  @doc """
  Renders a simple presence indicator showing users currently viewing a post.
  """
  attr :viewers, :list, required: true
  attr :class, :string, default: ""

  def presence_indicator(assigns) do
    assigns = assign(assigns, :viewer_count, length(assigns.viewers))

    ~H"""
    <div class={[
      "presence-indicator flex items-center gap-2 px-3 py-2 rounded-lg",
      "bg-white/10 backdrop-blur-sm border border-white/20",
      "text-white/90 text-sm",
      @class
    ]}>
      <%= if @viewer_count > 0 do %>
        <div class="flex items-center gap-2">
          <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span class="font-medium">
            {viewer_count_text(@viewer_count)}
          </span>
        </div>
      <% else %>
        <div class="flex items-center gap-2 text-white/60">
          <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
          <span>No one viewing</span>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper functions
  defp viewer_count_text(1), do: "1 person viewing"
  defp viewer_count_text(count), do: "#{count} people viewing"
end
