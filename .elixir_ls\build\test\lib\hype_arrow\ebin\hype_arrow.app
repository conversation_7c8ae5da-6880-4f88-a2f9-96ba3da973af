{application,hype_arrow,
             [{modules,['Elixir.HypeArrow','Elixir.HypeArrow.Accounts',
                        'Elixir.HypeArrow.Accounts.User',
                        'Elixir.HypeArrow.Accounts.UserNotifier',
                        'Elixir.HypeArrow.Accounts.UserToken',
                        'Elixir.HypeArrow.Application',
                        'Elixir.HypeArrow.DataCase','Elixir.HypeArrow.Mailer',
                        'Elixir.HypeArrow.Pagination',
                        'Elixir.HypeArrow.Presence',
                        'Elixir.HypeArrow.RealTime','Elixir.HypeArrow.Repo',
                        'Elixir.HypeArrow.Social',
                        'Elixir.HypeArrow.Social.Authorization',
                        'Elixir.HypeArrow.Social.Post','Elixir.HypeArrowWeb',
                        'Elixir.HypeArrowWeb.ChangesetJSON',
                        'Elixir.HypeArrowWeb.Components.AvatarComponents',
                        'Elixir.HypeArrowWeb.Components.ButtonComponents',
                        'Elixir.HypeArrowWeb.Components.CardComponents',
                        'Elixir.HypeArrowWeb.Components.EditPostModal',
                        'Elixir.HypeArrowWeb.Components.FormComponents',
                        'Elixir.HypeArrowWeb.Components.InfiniteScroll',
                        'Elixir.HypeArrowWeb.Components.LoadingComponents',
                        'Elixir.HypeArrowWeb.Components.SocialComponents',
                        'Elixir.HypeArrowWeb.Components.ToastComponents',
                        'Elixir.HypeArrowWeb.ConnCase',
                        'Elixir.HypeArrowWeb.ContentGridComponents',
                        'Elixir.HypeArrowWeb.CoreComponents',
                        'Elixir.HypeArrowWeb.Endpoint',
                        'Elixir.HypeArrowWeb.ErrorHTML',
                        'Elixir.HypeArrowWeb.ErrorJSON',
                        'Elixir.HypeArrowWeb.FallbackController',
                        'Elixir.HypeArrowWeb.Gettext',
                        'Elixir.HypeArrowWeb.LayoutComponents',
                        'Elixir.HypeArrowWeb.Layouts',
                        'Elixir.HypeArrowWeb.Live.Helpers',
                        'Elixir.HypeArrowWeb.Live.Hooks',
                        'Elixir.HypeArrowWeb.ModalComponents',
                        'Elixir.HypeArrowWeb.PageController',
                        'Elixir.HypeArrowWeb.PageHTML',
                        'Elixir.HypeArrowWeb.PostComponents',
                        'Elixir.HypeArrowWeb.PostController',
                        'Elixir.HypeArrowWeb.PostJSON',
                        'Elixir.HypeArrowWeb.PostLive.Index',
                        'Elixir.HypeArrowWeb.PostLive.PostUtils',
                        'Elixir.HypeArrowWeb.PresenceComponents',
                        'Elixir.HypeArrowWeb.Router',
                        'Elixir.HypeArrowWeb.Telemetry',
                        'Elixir.HypeArrowWeb.Theme',
                        'Elixir.HypeArrowWeb.UserAuth',
                        'Elixir.HypeArrowWeb.UserConfirmationController',
                        'Elixir.HypeArrowWeb.UserConfirmationHTML',
                        'Elixir.HypeArrowWeb.UserRegistrationController',
                        'Elixir.HypeArrowWeb.UserRegistrationHTML',
                        'Elixir.HypeArrowWeb.UserResetPasswordController',
                        'Elixir.HypeArrowWeb.UserResetPasswordHTML',
                        'Elixir.HypeArrowWeb.UserSessionController',
                        'Elixir.HypeArrowWeb.UserSessionHTML',
                        'Elixir.HypeArrowWeb.UserSettingsController',
                        'Elixir.HypeArrowWeb.UserSettingsHTML',
                        'Elixir.HypeArrowWeb.Utils.ErrorUtils',
                        'Elixir.HypeArrowWeb.Utils.FormatUtils',
                        'Elixir.HypeArrowWeb.Utils.PostUtils',
                        'Elixir.Inspect.HypeArrow.Accounts.User',
                        'Elixir.Pbkdf2']},
              {compile_env,[{hype_arrow,['Elixir.HypeArrowWeb.Gettext'],error},
                            {hype_arrow,[dev_routes],error}]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,runtime_tools,
                             phoenix,phoenix_ecto,ecto_sql,postgrex,
                             phoenix_html,phoenix_live_view,floki,
                             phoenix_live_dashboard,swoosh,finch,
                             telemetry_metrics,telemetry_poller,gettext,jason,
                             dns_cluster,bandit]},
              {description,"hype_arrow"},
              {registered,[]},
              {vsn,"0.1.0"},
              {mod,{'Elixir.HypeArrow.Application',[]}}]}.
