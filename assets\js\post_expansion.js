// Post expansion functionality - FULL-WIDTH ROW EXPANSION WITH RELIABLE SCROLLING
const setupPostExpansion = () => {
  // Store state
  let expandedPostId = null;
  let expandedPost = null;
  let scrollPosition = 0;
  let postPositions = new Map(); // Store positions of posts

  // Expose expandedPostId globally for other systems to check
  // Only define the property if it doesn't already exist to avoid "Cannot redefine property" errors
  if (!window.hasOwnProperty('expandedPostId')) {
    Object.defineProperty(window, 'expandedPostId', {
      get: () => expandedPostId,
      set: (value) => { expandedPostId = value; },
      configurable: true // Allow the property to be redefined if needed
    });
  }

  // Function to set up the expanded post layout
  // This ensures the post spans the full width while staying in its original row
  const setCorrectRowPositions = (expandedPost, grid) => {
    console.log('Setting up expanded post layout for:', expandedPost.id);

    // Get all posts in the grid
    const allPosts = Array.from(grid.querySelectorAll('.post-card'));
    if (allPosts.length === 0) {
      console.error('No posts found in the grid');
      return;
    }

    console.log(`Found ${allPosts.length} posts in the grid`);

    // Get the current grid template columns to determine how many columns we have
    const computedStyle = window.getComputedStyle(grid);
    const gridTemplateColumns = computedStyle.gridTemplateColumns;
    const columnCount = gridTemplateColumns.split(' ').length;

    console.log(`Grid has ${columnCount} columns`);

    // Find the index of the expanded post in the grid
    const expandedPostIndex = allPosts.indexOf(expandedPost);
    if (expandedPostIndex === -1) {
      console.error('Expanded post not found in grid');
      return;
    }

    // Calculate which row the expanded post should be in (1-based)
    const expandedPostRow = Math.floor(expandedPostIndex / columnCount) + 1;

    console.log(`Expanded post is at index ${expandedPostIndex}, should be in row ${expandedPostRow}`);

    // Clear any existing grid positioning on all posts to reset the layout
    allPosts.forEach(post => {
      if (post !== expandedPost) {
        post.style.removeProperty('grid-row');
        post.style.removeProperty('grid-column');
      }
    });

    // Set the expanded post to span the full width AND stay in its original row
    // Only set grid-column if the post doesn't already have it from CSS (server-rendered posts)
    const hasExpandedClass = expandedPost.classList.contains('expanded-post');
    const hasServerRenderedFlag = expandedPost.hasAttribute('data-expanded-by-server');

    if (!hasExpandedClass || !hasServerRenderedFlag) {
      // For client-side expanded posts, set grid-column
      expandedPost.style.setProperty('grid-column', '1 / -1', 'important');
    }
    // Always set grid-row to maintain original row position
    expandedPost.style.setProperty('grid-row', `${expandedPostRow}`, 'important');

    // Move other posts in the same row to the next row
    allPosts.forEach((post, index) => {
      if (post !== expandedPost) {
        const postRow = Math.floor(index / columnCount) + 1;

        if (postRow === expandedPostRow) {
          // This post is in the same row as the expanded post, move it to the next row
          const newRow = expandedPostRow + 1;
          post.style.setProperty('grid-row', `${newRow}`, 'important');
          console.log(`Moving post at index ${index} from row ${postRow} to row ${newRow}`);
        } else if (postRow > expandedPostRow) {
          // This post is in a row after the expanded post, shift it down by 1
          const newRow = postRow + 1;
          post.style.setProperty('grid-row', `${newRow}`, 'important');
          console.log(`Shifting post at index ${index} from row ${postRow} to row ${newRow}`);
        }
        // Posts in rows before the expanded post stay in their original positions
      }
    });

    // Force a reflow to ensure the grid layout is updated
    grid.offsetHeight;

    console.log('Finished setting up expanded post layout - post stays in original row');
  };

  // Expose setCorrectRowPositions globally for use in DOMContentLoaded handler
  // Only define if it doesn't already exist to avoid conflicts on multiple hook mounts
  if (!window.setCorrectRowPositions) {
    window.setCorrectRowPositions = setCorrectRowPositions;
  }

  // Function to apply visual expansion state (called from Phoenix events)
  // This only handles the visual aspects and does NOT communicate back to Phoenix
  // Only define if it doesn't already exist to avoid conflicts on multiple hook mounts
  if (!window.applyPostExpansionVisuals) {
    window.applyPostExpansionVisuals = (postId) => {
      console.log('🔧 APPLY POST EXPANSION VISUALS - Called for post:', postId);

      // Get the post element
      const post = document.getElementById(`post-${postId}`);
      if (!post) {
        console.error('Post not found:', postId);
        return;
      }

      // Check if this is a server-rendered expanded post - if so, skip all visual manipulation
      const hasServerRenderedFlag = post.hasAttribute('data-expanded-by-server');
      const hasExpandedClass = post.classList.contains('expanded-post');

      if (hasServerRenderedFlag && hasExpandedClass) {
        console.log(`🔧 SKIPPING visual expansion for server-rendered post ${postId} - CSS handles everything`);

        // Only set the internal state, no visual manipulation
        expandedPostId = postId;
        expandedPost = post;

        console.log(`Set internal expanded state from Phoenix event (server-rendered): post ${expandedPostId}`);
        return;
      }

      // If another post is expanded, collapse it first but don't scroll back
      if (expandedPostId && expandedPostId !== postId) {
        // Save the fact that we're switching posts
        const isSwitchingPosts = true;

        // Collapse without scrolling back (and without notifying Phoenix)
        collapsePostVisuals(isSwitchingPosts);
      }

      // Set the internal state to match the expanded post
      expandedPostId = postId;
      expandedPost = post;

      console.log(`Set internal expanded state from Phoenix event: post ${expandedPostId}`);

      // Apply the visual expansion state
      applyExpansionVisuals(post, postId);
    };
  }

  // Function to handle post expansion (called from user clicks)
  // This handles both visual aspects AND communicates with Phoenix
  // Only define if it doesn't already exist to avoid conflicts on multiple hook mounts
  if (!window.handlePostExpansion) {
    window.handlePostExpansion = (postId) => {
      console.log('🔧 HANDLE POST EXPANSION - Called for post:', postId);

      // Get the post element
      const post = document.getElementById(`post-${postId}`);
      if (!post) {
        console.error('Post not found:', postId);
        return;
      }

      // If this post is already expanded, collapse it
      if (expandedPostId === postId) {
        handlePostCollapse();
        return;
      }

      // If another post is expanded, collapse it first but don't scroll back
      if (expandedPostId) {
        // Save the fact that we're switching posts
        const isSwitchingPosts = true;

        // Collapse without scrolling back
        handlePostCollapse(isSwitchingPosts);
      }

      // Notify Phoenix about the expansion
      notifyPhoenixOfExpansion(postId);

      // Apply the visual expansion state
      applyExpansionVisuals(post, postId);
    };
  }

  // Function to safely push an event to Phoenix LiveView with timing safeguards
  const safePushEvent = (eventName, eventData, fallbackAction = null) => {
    return new Promise((resolve) => {
      try {
        // Look for the main LiveView container
        const liveViewContainer = document.querySelector('[data-phx-main]') || document.querySelector('[data-phx-view]') || document.body;

        if (!liveViewContainer) {
          console.warn('No LiveView container found');
          if (fallbackAction) fallbackAction();
          resolve(false);
          return;
        }

        if (!window.liveSocket) {
          console.warn('LiveSocket not available');
          if (fallbackAction) fallbackAction();
          resolve(false);
          return;
        }

        // Get the LiveView instance
        const view = window.liveSocket.getViewByEl(liveViewContainer);

        if (!view) {
          console.warn('No LiveView instance found');
          if (fallbackAction) fallbackAction();
          resolve(false);
          return;
        }

        // Validate that the view has the pushEvent method and is connected
        if (typeof view.pushEvent !== 'function') {
          console.warn('LiveView instance does not have pushEvent method');
          if (fallbackAction) fallbackAction();
          resolve(false);
          return;
        }

        // Additional validation for view state
        if (view.isDestroyed && view.isDestroyed()) {
          console.warn('LiveView instance is destroyed');
          if (fallbackAction) fallbackAction();
          resolve(false);
          return;
        }

        // Check if view is currently updating (this might prevent the race condition)
        if (view.isUpdating && view.isUpdating()) {
          console.warn('LiveView is currently updating, deferring event');
          // Defer the event until after the current update cycle
          setTimeout(() => {
            safePushEvent(eventName, eventData, fallbackAction).then(resolve);
          }, 50);
          return;
        }

        // Ensure event data is properly structured
        if (!eventData || typeof eventData !== 'object') {
          console.error('Invalid event data provided:', eventData);
          if (fallbackAction) fallbackAction();
          resolve(false);
          return;
        }

        // Additional safety: wrap the pushEvent call in a timeout to prevent blocking
        const timeoutId = setTimeout(() => {
          console.warn(`${eventName} event timed out, using fallback`);
          if (fallbackAction) fallbackAction();
          resolve(false);
        }, 1000);

        console.log(`Sending ${eventName} event to Phoenix:`, eventData);

        // Try to push the event
        try {
          view.pushEvent(eventName, eventData);
          clearTimeout(timeoutId);
          console.log(`${eventName} event sent successfully!`);
          resolve(true);
        } catch (pushError) {
          clearTimeout(timeoutId);
          console.error(`Error in pushEvent for ${eventName}:`, pushError);
          if (fallbackAction) fallbackAction();
          resolve(false);
        }
      } catch (error) {
        console.error(`Error sending ${eventName} event:`, error);
        if (fallbackAction) fallbackAction();
        resolve(false);
      }
    });
  };

  // Function to notify Phoenix about post expansion
  const notifyPhoenixOfExpansion = (postId) => {
    // Update the URL and notify Phoenix LiveView about the expanded post
    try {
      // Only update if the URL doesn't already contain this post ID
      const currentPath = window.location.pathname;
      const expectedPath = `/posts/${postId}`;

      if (currentPath !== expectedPath) {
        console.log(`Updating URL to ${expectedPath}`);

        // Try to notify Phoenix LiveView using the safe method
        const fallback = () => {
          console.log('Falling back to URL update for expansion');
          window.history.pushState({}, '', expectedPath);
        };

        safePushEvent('toggle-post', { id: String(postId) }, fallback).then((success) => {
          if (!success) {
            console.log('Failed to send toggle-post event, fallback already executed');
          }
        });
      }
    } catch (e) {
      console.error('Error updating URL:', e);
    }
  };

  // Function to apply visual expansion state to a post
  const applyExpansionVisuals = (post, postId) => {
    // CRITICAL: Get the correct scrollable container
    const scrollContainer = document.querySelector('.hype-main-content');
    if (!scrollContainer) {
      console.error('Scrollable container not found');
      return;
    }

    // Save the current scroll position of the container
    scrollPosition = scrollContainer.scrollTop;
    console.log('Saved container scroll position:', scrollPosition);

    // Save the post's position relative to the container
    const rect = post.getBoundingClientRect();

    // Store the container and position information
    postPositions.set(postId, {
      container: scrollContainer,
      scrollY: scrollPosition
    });

    console.log(`Saved position for post ${postId}:`, {
      scrollY: scrollPosition,
      postTop: rect.top
    });

    // Mark this post as expanded
    expandedPostId = postId;
    expandedPost = post;

    // Check if the post already has the expanded-post class (server-rendered)
    const wasAlreadyExpanded = post.classList.contains('expanded-post');

    // Add expanded class to the post (if not already present)
    if (!wasAlreadyExpanded) {
      post.classList.add('expanded-post');
    }

    // Add the has-expanded-post class to the grid container
    // Look for both .posts-grid and .content-grid to handle different container types
    const postsGrid = post.closest('.posts-grid') || post.closest('.content-grid');
    if (postsGrid) {
      // Add the appropriate class based on the container type
      if (postsGrid.classList.contains('content-grid')) {
        postsGrid.classList.add('has-expanded-item');
      } else {
        postsGrid.classList.add('has-expanded-post');
      }

      // Only set grid-column if this is not a server-rendered expanded post
      // For server-rendered posts, CSS already handles grid-column: 1 / -1
      if (!wasAlreadyExpanded) {
        // First, ensure the post is visible as expanded
        post.style.setProperty('grid-column', '1 / -1', 'important');
      }

      // Force a reflow to ensure the expanded class is applied
      post.offsetHeight;

      // Calculate and set the correct row for the expanded post
      // and adjust other posts in the same row
      setCorrectRowPositions(post, postsGrid);

      // Add a single backup call after animations should be complete
      setTimeout(() => {
        setCorrectRowPositions(post, postsGrid);
      }, 400);
    }

    // Check if the post already has a Phoenix-generated close button
    const existingCloseButton = post.querySelector('.post-card-header button');

    // If there's no Phoenix-generated close button, add our JavaScript one
    if (!existingCloseButton) {
      // First, remove any existing JS-added close buttons to avoid duplicates
      post.querySelectorAll('.post-collapse-button').forEach(btn => btn.remove());

      // Now add a single close button
      const closeButton = document.createElement('button');
      closeButton.className = 'post-collapse-button';
      closeButton.innerHTML = '×';
      closeButton.setAttribute('aria-label', 'Close');
      closeButton.addEventListener('click', (e) => {
        e.stopPropagation();
        handlePostCollapse();
      });
      post.appendChild(closeButton);
    } else {
      // Don't add our own event listener to the Phoenix-generated button
      // The Phoenix LiveView will handle the click event through phx-click
      // Adding our own event listener would interfere with Phoenix's event handling
    }

    // Scroll to the post using the container
    // Use a slightly longer delay when switching posts to ensure the DOM has updated
    const scrollDelay = expandedPostId ? 100 : 50;

    setTimeout(() => {
      try {
        const headerHeight = document.querySelector('.hype-navbar')?.offsetHeight || 0;
        const offset = headerHeight + 20;

        // Get the post's position relative to the container
        const postRect = post.getBoundingClientRect();
        const containerRect = scrollContainer.getBoundingClientRect();
        const relativeTop = postRect.top - containerRect.top + scrollContainer.scrollTop;

        // For full-width row expansion, we want to align the post to the top of the viewport
        // with a small offset for better visibility
        const headerOffset = 20; // Additional offset beyond the header height

        // Scroll the container to align the post to the top
        scrollContainer.scrollTo({
          top: Math.max(0, relativeTop - offset - headerOffset),
          behavior: 'smooth'
        });

        // Add multiple backup scroll attempts for reliability
        // This helps ensure the scroll position is maintained even after the post expands
        setTimeout(() => {
          scrollContainer.scrollTo({
            top: Math.max(0, relativeTop - offset - headerOffset),
            behavior: 'smooth'
          });
        }, 150);

        // Add another attempt after the transition should be complete
        setTimeout(() => {
          scrollContainer.scrollTo({
            top: Math.max(0, relativeTop - offset - headerOffset),
            behavior: 'auto'
          });
        }, 450);

        console.log('Scrolled container to post at position:', relativeTop - offset);
      } catch (e) {
        console.error('Error scrolling to post:', e);
      }
    }, scrollDelay);
  };

  // Function to collapse post visuals without notifying Phoenix
  const collapsePostVisuals = (isSwitchingPosts = false, skipScrollRestore = false) => {
    // If we have a specific post to collapse
    if (expandedPostId && expandedPost) {
      // Remove expanded class from the specific post
      expandedPost.classList.remove('expanded-post');
    } else {
      // If no specific post is tracked, collapse all expanded posts
      document.querySelectorAll('.expanded-post').forEach(post => {
        post.classList.remove('expanded-post');
      });

      // For cleanup when no specific post is tracked, we still need to clean up the grid
      // but we can skip the post-specific operations
    }

    // As a safety measure, ensure no other posts have the expanded class
    document.querySelectorAll('.expanded-post').forEach(post => {
      if (!expandedPost || post.id !== expandedPost.id) {
        post.classList.remove('expanded-post');
      }
    });

    // Remove the has-expanded-post/has-expanded-item class from the grid container
    // Look for both .posts-grid and .content-grid to handle different container types
    document.querySelectorAll('.posts-grid.has-expanded-post, .content-grid.has-expanded-item').forEach(grid => {
      // Remove the appropriate class based on the container type
      if (grid.classList.contains('content-grid')) {
        grid.classList.remove('has-expanded-item');
      } else {
        grid.classList.remove('has-expanded-post');
      }

      // Reset grid-row styles for all posts
      grid.querySelectorAll('.post-card').forEach(post => {
        post.style.removeProperty('grid-row');
        post.style.removeProperty('grid-column');
      });

      console.log('Reset grid-row and grid-column styles for all posts');
    });

    // Remove any JavaScript-added close button (only if expandedPost exists)
    if (expandedPost) {
      const closeButton = expandedPost.querySelector('.post-collapse-button');
      if (closeButton) {
        closeButton.remove();
      }
    }

    // Get the saved position (only if we have an expandedPostId)
    const position = expandedPostId ? postPositions.get(expandedPostId) : null;

    // Clear state BEFORE scrolling
    const postIdToRestore = expandedPostId;

    // Clear our local state
    expandedPostId = null;
    expandedPost = null;

    // If we have a saved position and we're not switching posts and not skipping scroll restore, restore it
    if (position && position.container && !isSwitchingPosts && !skipScrollRestore) {
      console.log(`Restoring position for post ${postIdToRestore}:`, position);

      // Get the container and scroll position
      const container = position.container;
      const scrollY = position.scrollY;

      // Scroll the container to the saved position
      try {
        // Immediate scroll
        container.scrollTop = scrollY;

        // Multiple backup attempts with setTimeout
        setTimeout(() => {
          container.scrollTop = scrollY;

          // Try again after a bit longer
          setTimeout(() => {
            container.scrollTop = scrollY;
          }, 50);

          // And again after even longer
          setTimeout(() => {
            container.scrollTop = scrollY;
          }, 150);
        }, 10);

        console.log('Restored container scroll position to:', scrollY);
      } catch (e) {
        console.error('Error restoring scroll position:', e);
      }
    } else if (isSwitchingPosts) {
      console.log('Switching posts - not scrolling back to previous position');
    } else if (skipScrollRestore) {
      console.log('Skipping scroll restore - edit scroll system will handle it');
    }
  };

  // Function to handle post collapse - PRIVATE to this module
  // isSwitchingPosts: if true, don't scroll back (we're about to show another post)
  // recentlySaved: if true, this post was recently saved from edit mode, so edit scroll system should handle restoration
  // skipPhoenixNotification: if true, don't notify Phoenix (used when Phoenix already initiated the collapse)
  const handlePostCollapse = (isSwitchingPosts = false, recentlySaved = false, skipPhoenixNotification = false) => {
    // Store the current expanded post info for Phoenix notification
    const currentExpandedPostId = expandedPostId;
    const currentExpandedPost = expandedPost;

    // Check if we're in an edit context by checking the global edit context flag
    const isInEditContext = window.postEditScrollContext && window.postEditScrollContext.isInEditContext;

    // If this post was recently saved, we should let the edit scroll system handle restoration
    const shouldSkipScrollRestore = isInEditContext || recentlySaved;

    if (shouldSkipScrollRestore) {
      console.log('Post collapse detected - skipping scroll restoration (edit context or recently saved)');
    }

    // First notify Phoenix LiveView that we're collapsing this post (unless we should skip it)
    // This is critical to keep the LiveView state in sync with our JavaScript state
    if (currentExpandedPostId && currentExpandedPost && !skipPhoenixNotification) {
      try {
        // Get the post ID from the element ID (format: "post-123")
        const postId = currentExpandedPost.id.split('-')[1];
        if (postId) {
          console.log(`Notifying Phoenix LiveView to collapse post ${postId}`);

          // Use the safe push event method with fallback
          const fallback = () => {
            console.log('Falling back to URL update to collapse post');
            window.history.pushState({}, '', '/');
          };

          safePushEvent('toggle-post', { id: String(postId) }, fallback).then((success) => {
            if (!success) {
              console.log('Failed to send collapse event to Phoenix, fallback already executed');
            }
          });
          // Don't return early - we still want to apply visual collapse
        }
      } catch (e) {
        console.error('Error notifying Phoenix LiveView:', e);
        // Fallback: Update the URL directly
        console.log('Exception occurred, falling back to URL update');
        window.history.pushState({}, '', '/');
      }
    } else if (skipPhoenixNotification) {
      console.log('Skipping Phoenix notification - collapse initiated by Phoenix');
    }

    // Then apply the visual collapse
    // Skip scroll restore if we're in edit context or if this was recently saved
    collapsePostVisuals(isSwitchingPosts, shouldSkipScrollRestore);
  };

  // Store the post ID that should enter edit mode after expansion
  let pendingEditPostId = null;

  // Function to handle post expansion for editing (DEPRECATED - now handled directly in Phoenix)
  // This is kept for backward compatibility but should not be used
  // Only define if it doesn't already exist to avoid conflicts on multiple hook mounts
  if (!window.handlePostExpansionForEdit) {
    window.handlePostExpansionForEdit = (postId) => {
      console.log('🔧 ===== DEPRECATED EDIT FUNCTION CALLED =====');
      console.log('🔧 This function is deprecated. Edit functionality is now handled directly in Phoenix.');
      console.log('🔧 Post ID:', postId);

      // For backward compatibility, just expand the post normally
      handlePostExpansion(postId);
    };
  }

  // Expose the collapse function globally
  // Only define if it doesn't already exist to avoid conflicts on multiple hook mounts
  if (!window.handlePostCollapse) {
    window.handlePostCollapse = (isSwitchingPosts = false, recentlySaved = false, skipPhoenixNotification = false) =>
      handlePostCollapse(isSwitchingPosts, recentlySaved, skipPhoenixNotification);
  }

  // Add a global function to collapse all posts (useful for debugging)
  // Only define if it doesn't already exist to avoid conflicts on multiple hook mounts
  if (!window.collapseAllPosts) {
    window.collapseAllPosts = () => {
      console.log('Collapsing all posts');

      // First check if there's a currently tracked expanded post
      if (expandedPostId && expandedPost) {
        // Use the regular collapse function to ensure Phoenix is notified
        handlePostCollapse();
        return;
      }

      // If no post is tracked but there are expanded posts, find the first one and collapse it
      const expandedPosts = document.querySelectorAll('.expanded-post');
      if (expandedPosts.length > 0) {
        // Get the first expanded post
        const firstExpandedPost = expandedPosts[0];

        // Get the post ID from the element ID (format: "post-123")
        const postId = firstExpandedPost.id.split('-')[1];
        if (postId) {
          // Use the safe push event method
          const fallback = () => {
            console.log('Falling back to URL update to collapse all posts');
            window.history.pushState({}, '', '/');
          };

          safePushEvent('toggle-post', { id: String(postId) }, fallback).then((success) => {
            if (success) {
              return; // Exit early as the Phoenix event will handle everything
            }
            // If not successful, continue with manual cleanup
            console.log('Phoenix event failed, performing manual cleanup');

            // As a last resort, just remove the class from all posts
            document.querySelectorAll('.expanded-post').forEach(post => {
              post.classList.remove('expanded-post');
            });

            // Clear state
            expandedPostId = null;
            expandedPost = null;
          });

          // Return early since we're handling cleanup in the promise
          return;
        }
      }

      // As a last resort, just remove the class from all posts
      document.querySelectorAll('.expanded-post').forEach(post => {
        post.classList.remove('expanded-post');
      });

      // Clear state
      expandedPostId = null;
      expandedPost = null;
    };
  }

  // Handle ESC key to collapse
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && expandedPostId) {
      // When using ESC key, we want to make sure Phoenix is notified
      // Use the safe push event method
      const fallback = () => {
        console.log('ESC key: falling back to regular collapse function');
        handlePostCollapse();
      };

      safePushEvent('toggle-post', { id: String(expandedPostId) }, fallback).then((success) => {
        if (success) {
          return; // Exit early as the Phoenix event will handle everything
        }
        // If not successful, fallback was already called
      });
    }
  });

  // Handle clicks on the close button inside expanded posts
  document.addEventListener('click', (e) => {
    if (e.target.closest('.post-collapse-button') && expandedPostId) {
      e.preventDefault();
      e.stopPropagation();

      // When clicking our JavaScript close button, we want to make sure Phoenix is notified
      // Use the safe push event method
      const fallback = () => {
        console.log('Close button: falling back to regular collapse function');
        handlePostCollapse();
      };

      safePushEvent('toggle-post', { id: String(expandedPostId) }, fallback).then((success) => {
        if (success) {
          return; // Exit early as the Phoenix event will handle everything
        }
        // If not successful, fallback was already called
      });
    }
  });

  // Handle maintain scroll position event from LiveView
  window.addEventListener('phx:maintain-scroll-position', (e) => {
    const { post_id } = e.detail;
    console.log('Maintaining scroll position for post:', post_id);

    // Get the post element
    const post = document.getElementById(`post-${post_id}`);
    if (!post) {
      console.error('Post not found:', post_id);
      return;
    }

    // Get the scrollable container
    const scrollContainer = document.querySelector('.hype-main-content');
    if (!scrollContainer) {
      console.error('Scrollable container not found');
      return;
    }

    // Get the grid container
    // Look for both .posts-grid and .content-grid to handle different container types
    const postsGrid = post.closest('.posts-grid') || post.closest('.content-grid');
    if (postsGrid) {
      // Add the appropriate class based on the container type
      if (postsGrid.classList.contains('content-grid')) {
        postsGrid.classList.add('has-expanded-item');
      } else {
        postsGrid.classList.add('has-expanded-post');
      }

      // First, ensure the post is visible as expanded
      post.style.setProperty('grid-column', '1 / -1', 'important');

      // Force a reflow to ensure the expanded class is applied
      post.offsetHeight;

      // Calculate and set the correct row for the expanded post
      setCorrectRowPositions(post, postsGrid);

      // Add a single backup call after a short delay
      setTimeout(() => {
        setCorrectRowPositions(post, postsGrid);
      }, 100);
    }

    // Calculate the position to scroll to
    const headerHeight = document.querySelector('.hype-navbar')?.offsetHeight || 0;
    const offset = headerHeight + 20;

    // Get the post's position relative to the container
    const postRect = post.getBoundingClientRect();
    const containerRect = scrollContainer.getBoundingClientRect();
    const relativeTop = postRect.top - containerRect.top + scrollContainer.scrollTop;

    // For full-width row expansion, we want to align the post to the top of the viewport
    // with a small offset for better visibility
    const headerOffset = 20; // Additional offset beyond the header height

    // Scroll the container to align the post to the top
    scrollContainer.scrollTo({
      top: Math.max(0, relativeTop - offset - headerOffset),
      behavior: 'smooth'
    });

    // Add multiple backup scroll attempts for reliability
    // This helps ensure the scroll position is maintained even after the post expands
    setTimeout(() => {
      scrollContainer.scrollTo({
        top: Math.max(0, relativeTop - offset - headerOffset),
        behavior: 'smooth'
      });
    }, 100);

    // Add another attempt after the transition should be complete
    setTimeout(() => {
      scrollContainer.scrollTo({
        top: Math.max(0, relativeTop - offset - headerOffset),
        behavior: 'auto'
      });
    }, 450);
  });

  // Handle post-expanded event from LiveView (DEPRECATED - now handled by applyPostExpansionVisuals)
  // This is kept for backward compatibility but should not be used
  window.addEventListener('phx:post-expanded', (e) => {
    const { id } = e.detail;
    console.log('🔧 DEPRECATED POST-EXPANDED EVENT - Received for post:', id);
    console.log('🔧 This event handler is deprecated. Use applyPostExpansionVisuals instead.');

    // For backward compatibility, call the new function
    window.applyPostExpansionVisuals(id);
  });
};

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // NOTE: We don't call setupPostExpansion() here because the LiveView hook will handle it
  // This handler only deals with cleanup of multiple expanded posts

  // Check if there are any expanded posts from Phoenix
  // If there are multiple, collapse all but the first one
  const expandedPosts = document.querySelectorAll('.expanded-post');
  if (expandedPosts.length > 1) {
    console.warn(`Found ${expandedPosts.length} expanded posts at initialization, fixing...`);

    // Keep only the first one expanded
    for (let i = 1; i < expandedPosts.length; i++) {
      expandedPosts[i].classList.remove('expanded-post');
    }
  }

  // If there's exactly one expanded post, just ensure the grid container has the right class
  // The LiveView hook will handle the rest
  if (expandedPosts.length === 1) {
    const currentExpandedPost = expandedPosts[0];
    console.log('DOMContentLoaded: Found server-rendered expanded post:', currentExpandedPost.id);

    // Look for both .posts-grid and .content-grid to handle different container types
    const postsGrid = currentExpandedPost.closest('.posts-grid') || currentExpandedPost.closest('.content-grid');

    if (postsGrid) {
      // Add the appropriate class based on the container type
      if (postsGrid.classList.contains('content-grid')) {
        postsGrid.classList.add('has-expanded-item');
      } else {
        postsGrid.classList.add('has-expanded-post');
      }
    }

    // Set a flag to indicate this post was handled by DOMContentLoaded
    // This helps other systems avoid duplicate processing
    currentExpandedPost.setAttribute('data-expanded-by-dom-loaded', 'true');
  }
});

export default setupPostExpansion;








